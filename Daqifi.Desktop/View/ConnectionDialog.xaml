<controls:MetroWindow x:Class="Daqifi.Desktop.View.ConnectionDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:controls="clr-namespace:MahApps.Metro.Controls;assembly=MahApps.Metro"
        xmlns:dialogService="clr-namespace:Daqifi.Desktop.DialogService"
        dialogService:DialogService.IsRegisteredView="True"              
        Title="Connect Device" ResizeMode="NoResize" Height="300" Width="450" Closing="MetroWindow_Closing" GlowBrush="{DynamicResource AccentColorBrush}" >
    <Grid>
        <TabControl>
            <TabItem Header="WiFi" controls:HeaderedControlHelper.HeaderFontSize="18">
                <Grid>
                    <controls:ProgressRing IsActive="{Binding HasNoWiFiDevices}" />
                    <Border>
                        <DockPanel Name="ConnectionUI" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" LastChildFill="True" Margin="5" >
                            <!-- Connect Button -->
                            <Button Name="btnConnect" DockPanel.Dock="Bottom" CommandParameter="{Binding ElementName=DeviceList, Path=SelectedItems}" Command="{Binding ConnectCommand}" Click="btnConnect_Click" Content="Connect" Width="100" Height="30" HorizontalAlignment="Right"  Style="{StaticResource MahApps.Styles.Button.Square.Accent }" controls:ControlsHelper.ContentCharacterCasing="Normal"/>

                            <!-- Device List -->
                            <ListView Name="DeviceList" ItemsSource="{Binding AvailableWiFiDevices}" SelectionMode="Multiple" Margin="5" Background="Transparent" BorderThickness="0">
                                <ListView.ItemTemplate>
                                    <DataTemplate>
                                        <Border >
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>
                                                <StackPanel  Grid.Column="1" Margin="5"  Orientation="Vertical">
                                                    <Label Content="{Binding Name}"  Padding="0"/>
                                                    <Label Content="{Binding IpAddress}" FontSize="10" Padding="0"/>
                                                    <Label Content="{Binding DeviceSerialNo}" FontSize="10"  Padding="0"/>
                                                    <TextBlock FontSize="10" Padding="0" Foreground="{Binding Foreground, RelativeSource={RelativeSource AncestorType=Label}}">
                                                        <Run Text="Firmware version :" />
                                                        <Run Text="{Binding DeviceVersion}" />
                                                    </TextBlock>
                                                </StackPanel>
                                                <Image Grid.Column="0" Source="../Images/Nq.png" Width="50" Height="50"/>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ListView.ItemTemplate>
                            </ListView>
                        </DockPanel>
                    </Border>
                </Grid>
            </TabItem>
            <TabItem Header="Manual WiFi"  controls:HeaderedControlHelper.HeaderFontSize="18">
                <Grid>
                    <Border>
                        <DockPanel HorizontalAlignment="Stretch" VerticalAlignment="Stretch" LastChildFill="True" Margin="5" >
                            <!-- Connect Button -->
                            <Button DockPanel.Dock="Bottom" Command="{Binding ConnectManualWifiCommand}" Click="btnConnect_Click" Content="Connect" Width="100" Height="30" HorizontalAlignment="Right" Style="{StaticResource MahApps.Styles.Button.Square.Accent }" controls:ControlsHelper.ContentCharacterCasing="Normal"/>

                            <!-- Manual IP Address -->
                            <StackPanel>
                                <Label Content="IP Address"/>
                                <TextBox Text="{Binding ManualIpAddress}"/>
                            </StackPanel>
                        </DockPanel>
                    </Border>
                </Grid>
            </TabItem>
            <TabItem Header="USB"  controls:HeaderedControlHelper.HeaderFontSize="18">
                <Grid>
                    <controls:ProgressRing IsActive="{Binding HasNoSerialDevices}" />
                    <Border>
                        <DockPanel Name="SerialConnectionUI" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" LastChildFill="True" Margin="5" >
                            <!-- Connect Button -->
                            <Button Name="btnConnectSerial" DockPanel.Dock="Bottom" CommandParameter="{Binding ElementName=SerialList, Path=SelectedItems}" Command="{Binding ConnectSerialCommand}" Click="btnConnect_Click" Content="Connect" Width="100" Height="30" HorizontalAlignment="Right" Style="{StaticResource MahApps.Styles.Button.Square.Accent }" controls:ControlsHelper.ContentCharacterCasing="Normal"/>

                            <!-- Device List -->
                            <ListView Name="SerialList" ItemsSource="{Binding AvailableSerialDevices}" SelectionMode="Multiple" Margin="5" Background="Transparent" BorderThickness="0">
                                <ListView.ItemTemplate>
                                    <DataTemplate>
                                        <Border >
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>
                                                <StackPanel Grid.Column="1" Margin="5" Orientation="Vertical">
                                                    <Label Content="{Binding Name}" Padding="0"/>
                                                    <Label Content="{Binding Port.PortName}" FontSize="10" Padding="0"/>
                                                    <Label Content="{Binding DeviceSerialNo}" FontSize="10" Padding="0"/>
                                                    <TextBlock FontSize="10" Padding="0" Foreground="{Binding Foreground, RelativeSource={RelativeSource AncestorType=Label}}">
                                                        <Run Text="Firmware version :" />
                                                        <Run Text="{Binding DeviceVersion}" />
                                                    </TextBlock>
                                                </StackPanel>
                                                <Image Grid.Column="0" Source="../Images/Nq.png" Width="50" Height="50"/>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ListView.ItemTemplate>
                            </ListView>
                        </DockPanel>
                    </Border>
                </Grid>
            </TabItem>
            <TabItem Header="Manual USB"  controls:HeaderedControlHelper.HeaderFontSize="18">
                <Grid>
                    <Border>
                        <DockPanel HorizontalAlignment="Stretch" VerticalAlignment="Stretch" LastChildFill="True" Margin="5" >
                            <!-- Connect Button -->
                            <Button DockPanel.Dock="Bottom" CommandParameter="{Binding ElementName=SerialList, Path=SelectedItems}" Command="{Binding ConnectManualSerialCommand}" Click="btnConnect_Click" Content="Connect" Width="100" Height="30" HorizontalAlignment="Right" Style="{StaticResource MahApps.Styles.Button.Square.Accent }" controls:ControlsHelper.ContentCharacterCasing="Normal"/>

                            <!-- Manual COM Port -->
                            <StackPanel>
                                <Label Content="COM Port"/>
                                <TextBox Text="{Binding ManualPortName}"/>
                            </StackPanel>
                        </DockPanel>
                    </Border>
                </Grid>
            </TabItem>
            <TabItem Header="Firmware"  controls:HeaderedControlHelper.HeaderFontSize="18">
                <Grid>
                    <controls:ProgressRing IsActive="{Binding HasNoHidDevices}" />
                    <Border>
                        <DockPanel Name="HidConnectionUI" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" LastChildFill="True" Margin="5" >
                            <!-- Open Firmware Button -->
                            <Button Name="BtnOpenFirmware" DockPanel.Dock="Bottom" CommandParameter="{Binding ElementName=HidList, Path=SelectedItems}" Command="{Binding ConnectHidCommand}" Content="Open Firmware" Width="100" Height="30" HorizontalAlignment="Right" Style="{StaticResource MahApps.Styles.Button.Square.Accent }" controls:ControlsHelper.ContentCharacterCasing="Normal"/>

                            <!-- Device List -->
                            <ListView Name="HidList" ItemsSource="{Binding AvailableHidDevices}" SelectionMode="Single" Margin="5" Background="Transparent" BorderThickness="0">
                                <ListView.ItemTemplate>
                                    <DataTemplate>
                                        <Border >
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>
                                                <StackPanel Grid.Column="1" Orientation="Vertical">
                                                    <Label Content="DAQifi Device (Firmware Mode)"/>
                                                </StackPanel>
                                                <Image Grid.Column="0" Source="../Images/Nq.png" Width="50" Height="50"/>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ListView.ItemTemplate>
                            </ListView>
                        </DockPanel>
                    </Border>
                </Grid>
            </TabItem>
        </TabControl>
    </Grid>
</controls:MetroWindow>

