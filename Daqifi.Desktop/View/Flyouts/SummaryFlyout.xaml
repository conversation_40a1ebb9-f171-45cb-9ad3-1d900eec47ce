<Controls:Flyout x:Class="Daqifi.Desktop.View.Flyouts.SummaryFlyout"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:Controls="clr-namespace:MahApps.Metro.Controls;assembly=MahApps.Metro"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="600"
             Width="{Binding FlyoutWidth}" Height="{Binding FlyoutHeight}" IsOpen="{Binding IsLogSummaryOpen, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" Position="Right"
             Header="Log Summary">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <Grid.RowDefinitions>
            <RowDefinition Height="auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <GroupBox Header="Settings" Grid.Row="0" Grid.Column="0" VerticalAlignment="Stretch" HorizontalAlignment="Stretch">
            <Grid Margin="15">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="75"/>
                </Grid.ColumnDefinitions>

                <Grid.RowDefinitions>
                    <RowDefinition/>
                    <RowDefinition/>
                    <RowDefinition/>
                </Grid.RowDefinitions>

                <Label Grid.Row="0" Grid.Column="0"
                           FontSize="16" FontWeight="Bold" Content="Log Session" />
                <Label Grid.Row="0" Grid.Column="1"
                             Content="{Binding SelectedLoggingSession.Name}" FontSize="16" HorizontalContentAlignment="Center"/>

                <Label Grid.Row="1" Grid.Column="0"
                           Content="Sample Size" FontSize="16" FontWeight="Bold" Margin="5"/>
                <Controls:NumericUpDown Grid.Row="1" Grid.Column="1" Value="{Binding SummaryLogger.SampleSize}" Minimum="1" Interval="1" Width="150" HorizontalAlignment="Left" ></Controls:NumericUpDown>

                <Label Grid.Row="2" Grid.Column="0"
                           Content="Status" FontSize="16" FontWeight="Bold" Margin="5"/>
                <Controls:ToggleSwitch Grid.Row="2" Grid.Column="1"
                                           Command="{Binding SummaryLogger.ToggleEnabledCommand}"
                                           IsOn="{Binding SummaryLogger.Enabled, Mode=OneWay}" OffContent="Stopped" OnContent="Running" HorizontalAlignment="Left"/>
                <Button Grid.Row="2" Grid.Column="2" Content="Reset"  Command="{Binding SummaryLogger.ResetCommand}"/>
            </Grid>
        </GroupBox>

        <ScrollViewer Grid.Row="1" Grid.Column="0" HorizontalAlignment="Stretch" VerticalAlignment="Stretch">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <GroupBox Header="Device" Grid.Row="0">
                    <Grid Margin="5">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="150"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Grid.RowDefinitions>
                            <RowDefinition/>
                            <RowDefinition/>
                            <RowDefinition/>
                            <RowDefinition/>
                            <RowDefinition/>
                            <RowDefinition/>
                            <RowDefinition/>
                        </Grid.RowDefinitions>

                        <Label Grid.Row="0" Grid.Column="0"
                           FontSize="16" FontWeight="Bold" Content="Updated" />
                        <Label Grid.Row="0" Grid.Column="1" Grid.ColumnSpan="3"
                            FontSize="16"
                            Content="{Binding SummaryLogger.LastUpdate}"
                            ContentStringFormat="HH:mm:ss.fffffff"
                            HorizontalContentAlignment="Left"/>

                        <Label Grid.Row="1" Grid.Column="0"
                           FontSize="16" FontWeight="Bold" Content="Elapsed Time (ms)" />
                        <Label Grid.Row="1" Grid.Column="1" Grid.ColumnSpan="3"
                           FontSize="16"
                           Content="{Binding SummaryLogger.ElapsedTime}"
                           HorizontalContentAlignment="Left" />

                        <Label Grid.Row="2" Grid.Column="0"
                           FontSize="16" FontWeight="Bold" Content="Sample Rate (hz)" />
                        <Label Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="3"
                           FontSize="16"
                           Content="{Binding SummaryLogger.SampleRate}" HorizontalContentAlignment="Center"/>

                        <Label Grid.Row="3" Grid.Column="0"
                           FontSize="16" FontWeight="Bold" Content="Statuses" />
                        <Label Grid.Row="3" Grid.Column="1"
                               FontSize="16"
                               Content="{Binding SummaryLogger.StatusList}" HorizontalContentAlignment="Right"/>

                        <Label Grid.Row="4" Grid.Column="1"
                           FontSize="16" FontWeight="Bold" Content="Min" />
                        <Label Grid.Row="4" Grid.Column="2"
                           FontSize="16" FontWeight="Bold" Content="Avg" />
                        <Label Grid.Row="4" Grid.Column="3"
                           FontSize="16" FontWeight="Bold" Content="Max" />

                        <Label Grid.Row="5" Grid.Column="0"
                           FontSize="16" FontWeight="Bold" Content="Delta (ticks)" />
                        <Label Grid.Row="5" Grid.Column="1"
                               FontSize="16"
                               Content="{Binding SummaryLogger.MinDelta}" HorizontalContentAlignment="Right"/>
                        <Label Grid.Row="5" Grid.Column="2"
                               FontSize="16"
                               Content="{Binding SummaryLogger.AverageDelta}" HorizontalContentAlignment="Right"/>
                        <Label Grid.Row="5" Grid.Column="3"
                               FontSize="16"
                               Content="{Binding SummaryLogger.MaxDelta}" HorizontalContentAlignment="Right"/>

                        <Label Grid.Row="6" Grid.Column="0"
                           FontSize="16" FontWeight="Bold" Content="Latency (ticks)" />
                        <Label Grid.Row="6" Grid.Column="1"
                               FontSize="16"
                               Content="{Binding SummaryLogger.MinLatency}" HorizontalContentAlignment="Right"/>
                        <Label Grid.Row="6" Grid.Column="2"
                               FontSize="16"
                               Content="{Binding SummaryLogger.AverageLatency}" HorizontalContentAlignment="Right"/>
                        <Label Grid.Row="6" Grid.Column="3"
                               FontSize="16"
                               Content="{Binding SummaryLogger.MaxLatency}" HorizontalContentAlignment="Right"/>
                    </Grid>
                </GroupBox>
                <GroupBox Header="Channels" Grid.Row="1">
                    <ListView ItemsSource="{Binding SummaryLogger.Channels}">
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <Grid Margin="5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="150"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <Grid.RowDefinitions>
                                        <RowDefinition />
                                        <RowDefinition />
                                        <RowDefinition />
                                        <RowDefinition />
                                    </Grid.RowDefinitions>

                                    <Label Grid.Row="0" Grid.Column="0"
                                       FontSize="16"
                                       Content="{Binding Name}" HorizontalContentAlignment="Left" />

                                    <Label Grid.Row="0" Grid.Column="1" Grid.ColumnSpan="1"
                                       FontSize="16"
                                       Content="{Binding LastUpdate}"
                                       ContentStringFormat="HH:mm:ss.fffffff"
                                       HorizontalContentAlignment="Left" />

                                    <Label Grid.Row="0" Grid.Column="2"
                                       FontSize="16"
                                       Content="Count" HorizontalContentAlignment="Right" />
                                    
                                    <Label Grid.Row="0" Grid.Column="3"
                                       FontSize="16"
                                       Content="{Binding SampleCount}" HorizontalContentAlignment="Right" />

                                    <Label Grid.Row="1" Grid.Column="0"
                                       FontSize="16" FontWeight="Bold" Content="Sample Rate (hz)" />
                                    <Label Grid.Row="1" Grid.Column="2"
                                       FontSize="16"
                                       Content="{Binding SampleRate}" HorizontalContentAlignment="Right"/>
                                    
                                    <Label Grid.Row="2" Grid.Column="0"
                                        FontSize="16" FontWeight="Bold" Content="Delta" />
                                    <Label Grid.Row="2" Grid.Column="1"
                                       FontSize="16"
                                       Content="{Binding MinDelta}" HorizontalContentAlignment="Right" />
                                    <Label Grid.Row="2" Grid.Column="2"
                                       FontSize="16"
                                       Content="{Binding AverageDelta}" HorizontalContentAlignment="Right" />
                                    <Label Grid.Row="2" Grid.Column="3"
                                       FontSize="16"
                                       Content="{Binding MaxDelta}" HorizontalContentAlignment="Right" />

                                    <Label Grid.Row="3" Grid.Column="0"
                                        FontSize="16" FontWeight="Bold" Content="Value" />
                                    <Label Grid.Row="3" Grid.Column="1"
                                        FontSize="16"
                                        Content="{Binding MinValue}" HorizontalContentAlignment="Right" />
                                    <Label Grid.Row="3" Grid.Column="2"
                                       FontSize="16"
                                       Content="{Binding AverageValue}" HorizontalContentAlignment="Right" />
                                    <Label Grid.Row="3" Grid.Column="3"
                                       FontSize="16"
                                       Content="{Binding MaxValue}" HorizontalContentAlignment="Right" />
                                </Grid>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </ListView>
                </GroupBox>
            </Grid>
        </ScrollViewer>
    </Grid>
</Controls:Flyout>