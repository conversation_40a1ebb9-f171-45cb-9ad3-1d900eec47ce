<Controls:Flyout x:Class="Daqifi.Desktop.View.Flyouts.ChannelsFlyout"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:Controls="clr-namespace:MahApps.Metro.Controls;assembly=MahApps.Metro"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="600"
             Width="{Binding FlyoutWidth}" Height="{Binding FlyoutHeight}" IsOpen="{Binding IsChannelSettingsOpen, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" Position="Right" Header="Channel Settings">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <StackPanel Grid.Column="0">
            <!-- Channel Status -->
            <GroupBox Header="Channel Status">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <TextBlock Text="Status:" Grid.Row="0" Grid.Column="0" Margin="0,0,10,5"/>
                    <TextBlock Text="{Binding SelectedChannel.IsActive, Converter={StaticResource BoolToActiveStatus}}"
                              Grid.Row="0" Grid.Column="1"
                              Foreground="{Binding SelectedChannel.IsActive, Converter={StaticResource BoolToStatusColor}}"/>
                    
                    <TextBlock Text="Type:" Grid.Row="1" Grid.Column="0" Margin="0,0,10,5"/>
                    <TextBlock Text="{Binding SelectedChannel.TypeString, Mode=OneWay}" Grid.Row="1" Grid.Column="1"/>
                    
                    <TextBlock Text="Device:" Grid.Row="2" Grid.Column="0" Margin="0,0,10,5"/>
                    <TextBlock Text="{Binding SelectedChannel.DeviceName}" Grid.Row="2" Grid.Column="1"/>
                    
                    <TextBlock Text="Voltage Range:" Grid.Row="3" Grid.Column="0" Margin="0,0,10,5"/>
                    <TextBlock Text="0-5V" Grid.Row="3" Grid.Column="1"/>
                </Grid>
            </GroupBox>

            <!-- Channel Configuration -->
            <GroupBox Header="Channel Configuration">
                <StackPanel>
                    <Label Content="Name"/>
                    <TextBox Text="{Binding SelectedChannel.Name}" 
                             Height="30" 
                             FontSize="14" 
                             VerticalAlignment="Center" 
                             VerticalContentAlignment="Center"
                             Margin="0,0,0,10"/>
                    
                    <Label Content="Color"/>
                    <Button Background="{Binding SelectedChannel.ChannelColorBrush}" 
                            Width="50" 
                            Height="30" 
                            Command="{Binding ShowSelectColorDialogCommand}" 
                            CommandParameter="{Binding SelectedChannel}"
                            Margin="0,0,0,10"/>
                </StackPanel>
            </GroupBox>
        </StackPanel>

        <StackPanel Grid.Column="1">

            <!-- Set Direction -->
            <GroupBox Header="I/O Direction" Visibility="{Binding Path=SelectedChannel.IsBidirectional, Converter={StaticResource BoolToVis}}">
                <Grid >
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Controls:ToggleSwitch Grid.Row="0" IsOn="{Binding SelectedChannel.IsOutput, Mode=TwoWay}" OffContent="Input" OnContent="Output" HorizontalAlignment="Center"/>
                </Grid>
            </GroupBox>

            <!-- Set Digital Output -->
            <Grid Visibility="{Binding Path=SelectedChannel.IsDigital, Converter={StaticResource BoolToVis}}">
                <GroupBox Header="Digital Output" Visibility="{Binding Path=SelectedChannel.IsOutput, Converter={StaticResource BoolToVis}}">
                    <Grid >
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Controls:ToggleSwitch Grid.Row="0" IsOn="{Binding SelectedChannel.IsDigitalOn, Mode=TwoWay}" OffContent="Off" OnContent="On" HorizontalAlignment="Center"/>
                    </Grid>
                </GroupBox>
            </Grid>
            
            <!-- Scaling Configuration -->
            <GroupBox Header="Scaling" Visibility="{Binding Path=SelectedChannel.IsAnalog, Converter={StaticResource BoolToVis}}">
                <StackPanel>
                    <Controls:ToggleSwitch IsOn="{Binding SelectedChannel.IsScalingActive, Mode=TwoWay}" 
                                           OffContent="Scaling Off" 
                                           OnContent="Scaling On" 
                                           HorizontalAlignment="Left"
                                           Margin="0,0,0,10"/>
                    
                    <StackPanel Visibility="{Binding Path=SelectedChannel.IsScalingActive, Converter={StaticResource BoolToVis}}">
                        <Label Content="Expression"/>
                        <TextBox Text="{Binding SelectedChannel.ScaleExpression, Delay=500, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" 
                                 Height="30" 
                                 FontSize="14" 
                                 VerticalAlignment="Center" 
                                 VerticalContentAlignment="Center"/>
                        
                        <Label Content="Not a valid scaling expression!" 
                               Visibility="{Binding Path=SelectedChannel.HasValidExpression, Converter={StaticResource InvertedBoolToVis}}" 
                               Foreground="Orange" 
                               HorizontalAlignment="Left"
                               Margin="0,5,0,0"/>
                        
                        <TextBlock Text="Example: x + 5" 
                                   Foreground="{DynamicResource MahApps.Brushes.Gray3}"
                                   Margin="0,5,0,0"/>
                    </StackPanel>
                </StackPanel>
            </GroupBox>
            
        </StackPanel>
    </Grid>
</Controls:Flyout>
