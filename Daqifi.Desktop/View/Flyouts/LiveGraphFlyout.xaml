<Controls:Flyout x:Class="Daqifi.Desktop.View.Flyouts.LiveGraphFlyout"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:Controls="clr-namespace:MahApps.Metro.Controls;assembly=MahApps.Metro"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="600"
                 Width="{Binding FlyoutWidth}" Height="{Binding FlyoutHeight}" IsOpen="{Binding IsLiveGraphSettingsOpen, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" Position="Right" Header="Live Graph Settings">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        <GroupBox Grid.Column="1" Grid.Row="0" Header="Grid Settings">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <Label Grid.Row="0" Grid.Column="0" Content="Major X-Axis" VerticalAlignment="Center"/>
                <Controls:ToggleSwitch Grid.Row="0" Grid.Column="1" IsOn="{Binding Plotter.ShowingMajorXAxisGrid, Mode=TwoWay}" OffContent="Off" OnContent="On" HorizontalAlignment="Left"/>
                <Label Grid.Row="1" Grid.Column="0" Content="Minor X-Axis" VerticalAlignment="Center"/>
                <Controls:ToggleSwitch Grid.Row="1" Grid.Column="1" IsOn="{Binding Plotter.ShowingMinorXAxisGrid, Mode=TwoWay}" OffContent="Off" OnContent="On" HorizontalAlignment="Left"/>
                <Label Grid.Row="2" Grid.Column="0" Content="Major Y-Axis" VerticalAlignment="Center"/>
                <Controls:ToggleSwitch Grid.Row="2" Grid.Column="1" IsOn="{Binding Plotter.ShowingMajorYAxisGrid, Mode=TwoWay}" OffContent="Off" OnContent="On" HorizontalAlignment="Left"/>
                <Label Grid.Row="3" Grid.Column="0" Content="Minor Y-Axis" VerticalAlignment="Center"/>
                <Controls:ToggleSwitch Grid.Row="3" Grid.Column="1" IsOn="{Binding Plotter.ShowingMinorYAxisGrid, Mode=TwoWay}" OffContent="Off" OnContent="On" HorizontalAlignment="Left"/>
            </Grid>
        </GroupBox>
        <GroupBox Grid.Column="0" Grid.Row="0" Header="Graph Precision">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Label Grid.Row="0" Content="{Binding Plotter.Precision, UpdateSourceTrigger=PropertyChanged}" HorizontalAlignment="Center" VerticalAlignment="Center" HorizontalContentAlignment="Center" FontSize="20"/>
                <Slider Grid.Row="1" HorizontalAlignment="Stretch" Minimum="0" Maximum="10" TickFrequency="1" IsSnapToTickEnabled="True" Value="{Binding Plotter.Precision, UpdateSourceTrigger=PropertyChanged}"/>
            </Grid>
        </GroupBox>
    </Grid>
</Controls:Flyout>
