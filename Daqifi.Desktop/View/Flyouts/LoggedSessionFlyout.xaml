<Controls:Flyout x:Class="Daqifi.Desktop.View.Flyouts.LoggedSessionFlyout"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:Controls="clr-namespace:MahApps.Metro.Controls;assembly=MahApps.Metro"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="600"
             Width="{Binding FlyoutWidth}" Height="{Binding FlyoutHeight}" IsOpen="{Binding IsLoggingSessionSettingsOpen, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" Position="Right" Header="Logged Session Settings">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        <GroupBox Grid.Row="0" Grid.Column="0" Header="Name">
            <TextBox Text="{Binding LoggedSessionName,UpdateSourceTrigger=PropertyChanged}" TextChanged="UpdateSessionName" Height="50" FontSize="16" VerticalAlignment="Center" VerticalContentAlignment="Center"/>
        </GroupBox>
    </Grid>
</Controls:Flyout>
