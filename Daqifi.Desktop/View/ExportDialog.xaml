<controls:MetroWindow x:Class="Daqifi.Desktop.View.ExportDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:controls="clr-namespace:MahApps.Metro.Controls;assembly=MahApps.Metro"
        Title="Export Logging Session" ResizeMode="NoResize" Height="350" Width="600" GlowBrush="{DynamicResource AccentColorBrush}">

    <Grid Margin="5">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="Auto"/>
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Export Location -->
        <GroupBox Grid.Row="0" Grid.ColumnSpan="2" Header="Export Location">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <TextBox Grid.Column="0" HorizontalAlignment="Stretch" Text="{Binding ExportFilePath, Mode=OneWay}" IsEnabled="False"/>
                <Button Grid.Column="1" Content="Browse" Command="{Binding BrowseExportPathCommand}"/>
            </Grid>
        </GroupBox>

        <!-- Export Type -->
        <GroupBox Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2" Header="Export Type">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <RadioButton Grid.Column="0" Grid.Row="0" Content="All Samples" IsChecked="{Binding ExportAllSelected}"/>
                <RadioButton Grid.Column="0" Grid.Row="1" Padding="5" Content="Average Every &quot;N&quot; Samples" IsChecked="{Binding ExportAverageSelected}"/>
                <TextBox Grid.Column="1" Grid.Row="1" Width="50" Text="{Binding AverageQuantity, Mode=TwoWay}"/>
            </Grid>
        </GroupBox>

        <GroupBox Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2" Header="Timestamp Format" Margin="0,10,0,0">
            <StackPanel Orientation="Horizontal">
                <RadioButton Content="Absolute Time" IsChecked="True" Padding="5" />
                <RadioButton Content="Relative Time" IsChecked="{Binding ExportRelativeTime}" Margin="0,0,10,0"/>
            </StackPanel>
        </GroupBox>

        <GroupBox Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="2" Header="Exporting file" >
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Progress Bar -->
                <controls:MetroProgressBar Grid.Column="0" Grid.Row="0" Height="25" Minimum="0" Maximum="100" 
                                   Value="{Binding ExportProgress}" 
                                   Visibility="{Binding IsExporting, Converter={StaticResource BooleanToVisibilityConverter}}" Margin="5"/>

                <!-- Cancel Button -->
                <Button Grid.Column="1" Grid.Row="0" Content="Cancel" Command="{Binding CancelExportCommand}" 
                Visibility="{Binding IsExporting, Converter={StaticResource BooleanToVisibilityConverter}}" Margin="5"/>
                <Label Grid.Row="1" Grid.Column="0" Content="{Binding ExportProgressText}"  HorizontalAlignment="Center"/>
            </Grid>
        </GroupBox>

        <DockPanel Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="2" Grid.RowSpan="3"  HorizontalAlignment="Right" VerticalAlignment="Bottom">
            <Button Click="btnCancel_Click" Content="Cancel" Width="100" Height="30"  Style="{StaticResource  MahApps.Styles.Button.Square}" controls:ControlsHelper.ContentCharacterCasing="Normal"/>
            <Button Content="Export"  Command="{Binding ExportLoggingSessionsCommand}" IsEnabled="{Binding IsExporting, Converter={StaticResource BooleanToInverse}}"    Width="100" Height="30" Style="{StaticResource MahApps.Styles.Button.Square.Accent }"  controls:ControlsHelper.ContentCharacterCasing="Normal"/>
        </DockPanel>
    </Grid>
</controls:MetroWindow>
